import React, { useState } from 'react';
import { Bell, Calendar, Clock, AlertTriangle, CheckCircle, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs';
import { useNotifications, usePendingEvents, useNotificationStats } from '../../../hooks/useNotifications';
import { UserNotification, DiagnosticEvent } from '../../../types/notifications';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

/**
 * Composant principal du centre de notifications
 */
export const NotificationCenter: React.FC = () => {
  const { notifications, unreadCount, markAsRead } = useNotifications();
  const { todayEvents, overdueEvents, upcomingEvents } = usePendingEvents();
  const { stats } = useNotificationStats();
  const [activeTab, setActiveTab] = useState('notifications');

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Erreur lors du marquage de la notification:', error);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* En-tête avec statistiques */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="h-8 w-8 text-green-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Centre de Notifications</h1>
            <p className="text-gray-600">Gérez vos rappels et suivez vos plantes</p>
          </div>
        </div>
        
        {stats && (
          <div className="flex gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.pendingEvents}</div>
              <div className="text-sm text-gray-600">En attente</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.overdueEvents}</div>
              <div className="text-sm text-gray-600">En retard</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{unreadCount}</div>
              <div className="text-sm text-gray-600">Non lues</div>
            </div>
          </div>
        )}
      </div>

      {/* Onglets principaux */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-1">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="events" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Événements
          </TabsTrigger>
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Tableau de bord
          </TabsTrigger>
        </TabsList>

        {/* Onglet Notifications */}
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications récentes
              </CardTitle>
            </CardHeader>
            <CardContent>
              {notifications.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune notification pour le moment</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {notifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={handleMarkAsRead}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Événements */}
        <TabsContent value="events" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Événements d'aujourd'hui */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-600">
                  <Calendar className="h-5 w-5" />
                  Aujourd'hui ({todayEvents.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {todayEvents.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">Aucun événement aujourd'hui</p>
                ) : (
                  <div className="space-y-2">
                    {todayEvents.map((event) => (
                      <EventItem key={event.id} event={event} />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Événements en retard */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600">
                  <AlertTriangle className="h-5 w-5" />
                  En retard ({overdueEvents.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {overdueEvents.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">Aucun événement en retard</p>
                ) : (
                  <div className="space-y-2">
                    {overdueEvents.map((event) => (
                      <EventItem key={event.id} event={event} isOverdue />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Événements à venir */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-600">
                <Clock className="h-5 w-5" />
                À venir cette semaine ({upcomingEvents.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {upcomingEvents.length === 0 ? (
                <p className="text-gray-500 text-center py-4">Aucun événement prévu cette semaine</p>
              ) : (
                <div className="grid gap-2 md:grid-cols-2">
                  {upcomingEvents.map((event) => (
                    <EventItem key={event.id} event={event} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Tableau de bord */}
        <TabsContent value="dashboard" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Total événements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.pendingEvents || 0}</div>
                <p className="text-xs text-gray-600">événements actifs</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Notifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.totalNotifications || 0}</div>
                <p className="text-xs text-gray-600">{unreadCount} non lues</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">En retard</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats?.overdueEvents || 0}</div>
                <p className="text-xs text-gray-600">nécessitent attention</p>
              </CardContent>
            </Card>
          </div>

          {/* Répartition par priorité */}
          {stats && (
            <Card>
              <CardHeader>
                <CardTitle>Répartition par priorité</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-red-600">
                      {stats.notificationsByPriority.urgent || 0}
                    </div>
                    <div className="text-sm text-gray-600">Urgent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-orange-600">
                      {stats.notificationsByPriority.high || 0}
                    </div>
                    <div className="text-sm text-gray-600">Élevée</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-yellow-600">
                      {stats.notificationsByPriority.medium || 0}
                    </div>
                    <div className="text-sm text-gray-600">Moyenne</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-600">
                      {stats.notificationsByPriority.low || 0}
                    </div>
                    <div className="text-sm text-gray-600">Faible</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

/**
 * Composant pour afficher une notification individuelle
 */
interface NotificationItemProps {
  notification: UserNotification;
  onMarkAsRead: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onMarkAsRead }) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-l-red-500 bg-red-50';
      case 'high': return 'border-l-orange-500 bg-orange-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-green-500 bg-green-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  return (
    <div className={`p-4 border-l-4 rounded-r-lg ${getPriorityColor(notification.priority)} ${!notification.read ? 'shadow-md' : 'opacity-75'}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-gray-900">{notification.title}</h4>
            {!notification.read && (
              <Badge variant="secondary" className="text-xs">Nouveau</Badge>
            )}
          </div>
          <p className="text-gray-700 text-sm mb-2">{notification.message}</p>
          <p className="text-xs text-gray-500">
            {formatDistanceToNow(notification.createdAt.toDate(), { addSuffix: true, locale: fr })}
          </p>
        </div>
        {!notification.read && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onMarkAsRead(notification.id)}
            className="ml-2"
          >
            <CheckCircle className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

/**
 * Composant pour afficher un événement individuel
 */
interface EventItemProps {
  event: DiagnosticEvent;
  isOverdue?: boolean;
}

const EventItem: React.FC<EventItemProps> = ({ event, isOverdue = false }) => {
  return (
    <div className={`p-3 rounded-lg border ${isOverdue ? 'border-red-200 bg-red-50' : 'border-gray-200 bg-white'}`}>
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-medium text-gray-900 text-sm">{event.title}</h4>
        <Badge variant={isOverdue ? 'destructive' : 'secondary'} className="text-xs">
          {event.priority}
        </Badge>
      </div>
      <p className="text-gray-600 text-xs mb-2">{event.plantName}</p>
      <p className="text-gray-700 text-sm">{event.nextActionType}</p>
      <p className="text-xs text-gray-500 mt-1">
        {event.nextActionDate.toDate().toLocaleDateString('fr-FR')}
      </p>
    </div>
  );
};
